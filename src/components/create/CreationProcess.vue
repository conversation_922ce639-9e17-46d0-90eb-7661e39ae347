<template>
  <div class="creation-process" :class="{ 'chat-visible': chatPanelVisible }">
    <!-- 添加动态背景效果 -->
    <!-- <div class="background-animation">
      <div class="light-effect light-effect-1"></div>
      <div class="light-effect light-effect-2"></div>
      <div class="light-effect light-effect-3"></div>
    </div> -->

    <el-tabs :model-value="activeTab" @update:model-value="$emit('update:activeTab', $event)" class="creation-tabs"
      tab-position="top">


      <!-- 新增设计模块组件标签页 -->
      <!-- <el-tab-pane name="design">
        <template #label>
          <div class="tab-label">
            <div class="tab-icon">
              <el-icon>
                <SetUp />
              </el-icon>
            </div>
            <div class="tab-text">
              <span class="tab-title">设计</span>
              <span class="tab-desc">故事设计</span>
            </div>
            <div v-if="designResult" class="tab-status">
              <el-icon v-if="designResult.status === 'completed'" class="status-icon completed">
                <Check />
              </el-icon>
              <el-icon v-else-if="designResult.status === 'in-progress'" class="status-icon in-progress">
                <Loading />
              </el-icon>
            </div>
          </div>
        </template>
        <div v-if="!storyDesign" class="empty-state final-empty-state">
          <el-icon class="empty-icon">
            <SetUp />
          </el-icon>
          <div class="empty-text">故事设计暂未生成</div>
          <div class="empty-desc">请跟智灵描述你想要生成的故事设计</div>
        </div>
        <StoryDesign v-else :design-result="designResult" :story-design="storyDesign"
          @update:storyDesign="handleStoryDesignUpdate" :voices="voices" :isLoadingVoices="isLoadingVoices"
          @refresh-voices="$emit('refresh-voices')" />
      </el-tab-pane> -->

      <el-tab-pane name="edit">
        <template #label>
          <div class="tab-label">
            <div class="tab-icon">
              <el-icon>
                <User />
              </el-icon>
            </div>
            <div class="tab-text">
              <span class="tab-title">角色</span>
              <span class="tab-desc">角色形象</span>
            </div>
            <div v-if="editResult" class="tab-status">
              <el-icon v-if="editResult.status === 'completed'" class="status-icon completed">
                <Check />
              </el-icon>
              <el-icon v-else-if="editResult.status === 'in-progress'" class="status-icon in-progress">
                <Loading />
              </el-icon>
            </div>
          </div>
        </template>
        <div v-if="!shotRoles || !shotRoles.characters || shotRoles.characters.length === 0"
          class="empty-state final-empty-state">
          <el-icon class="empty-icon">
            <User />
          </el-icon>
          <div class="empty-text">角色暂未生成</div>
          <div class="empty-desc">请跟智灵描述你想要生成的角色形象和属性</div>
        </div>
        <CharacterGeneration v-else :edit-result="editResult" :shot-roles="shotRoles" :conversation-id="conversationId"
          :voices="voices" :isLoadingVoices="isLoadingVoices" @update:shotRoles="handleShotRolesUpdate" @update-ratio="handleUpdateRatio" />
      </el-tab-pane>

      <!-- <el-tab-pane name="script">
        <template #label>
          <div class="tab-label">
            <div class="tab-icon">
              <el-icon>
                <Document />
              </el-icon>
            </div>
            <div class="tab-text">
              <span class="tab-title">故事</span>
              <span class="tab-desc">故事大纲</span>
            </div>
            <div v-if="scriptResult" class="tab-status">
              <el-icon v-if="scriptResult.status === 'completed'" class="status-icon completed">
                <Check />
              </el-icon>
              <el-icon v-else-if="scriptResult.status === 'in-progress'" class="status-icon in-progress">
                <Loading />
              </el-icon>
            </div>
          </div>
        </template>
        <div v-if="!shotStory || !shotStory.chapters || shotStory.chapters.length === 0"
          class="empty-state final-empty-state">
          <el-icon class="empty-icon">
            <Document />
          </el-icon>
          <div class="empty-text">故事暂未生成</div>
          <div class="empty-desc">请跟智灵描述你想要生成故事</div>
        </div>
        <StoryGeneration v-else :script-result="scriptResult" :shot-story="shotStory"
          @update:shotStory="handleShotStoryUpdate" />
      </el-tab-pane> -->

      <!-- 场景 -->
      <!-- <el-tab-pane name="voice">
        <template #label>
          <div class="tab-label">
            <div class="tab-icon">
              <el-icon>
                <Picture />
              </el-icon>
            </div>
            <div class="tab-text">
              <span class="tab-title">场景</span>
              <span class="tab-desc">场景和环境</span>
            </div>
            <div v-if="voiceResult" class="tab-status">
              <el-icon v-if="voiceResult.status === 'completed'" class="status-icon completed">
                <Check />
              </el-icon>
              <el-icon v-else-if="voiceResult.status === 'in-progress'" class="status-icon in-progress">
                <Loading />
              </el-icon>
            </div>
          </div>
        </template>
        <div v-if="!shotScenes || !shotScenes.scenes || shotScenes.scenes.length === 0" class="empty-state final-empty-state">
          <el-icon class="empty-icon"><Picture /></el-icon>
          <div class="empty-text">场景暂未生成</div>
          <div class="empty-desc">请跟智灵描述你想要设计的场景和环境</div>
        </div>
        <SceneGeneration v-else :voice-result="voiceResult" :shot-scenes="shotScenes" :conversation-id="conversationId" />
      </el-tab-pane> -->

      <el-tab-pane name="animation">
        <template #label>
          <div class="tab-label">
            <div class="tab-icon">
              <el-icon>
                <VideoCamera />
              </el-icon>
            </div>
            <div class="tab-text">
              <span class="tab-title">分镜</span>
              <span class="tab-desc">镜头和旁白</span>
            </div>
            <div v-if="animationResult" class="tab-status">
              <el-icon v-if="animationResult.status === 'completed'" class="status-icon completed">
                <Check />
              </el-icon>
              <el-icon v-else-if="animationResult.status === 'in-progress'" class="status-icon in-progress">
                <Loading />
              </el-icon>
            </div>
          </div>
        </template>
        <div v-if="shotsCount <= 0" class="empty-state final-empty-state">
          <el-icon class="empty-icon">
            <VideoCamera />
          </el-icon>
          <div class="empty-text">分镜暂未生成</div>
          <div class="empty-desc">请跟智灵描述你想要生成的镜头和旁白设计</div>
        </div>

        <!-- <ShotGeneration v-else :animation-result="animationResult" :shot-shots="shotShots" :active-tab="activeTab"
          :conversation-id="conversationId" :image-size="outputRatio" @update:shotShots="handleShotShotsUpdate" /> -->
          
        <ShotGeneration :animation-result="{ status: 'completed' }"
          :conversation-id="conversationId" :image-size="imageRatio"
          :active-tab="activeTab"
          @update-shot-shots-count="handleShotShotsCountUpdate" />

      </el-tab-pane>

      <!-- <el-tab-pane name="final">
        <template #label>
          <div class="tab-label">
            <div class="tab-icon">
              <el-icon>
                <Star />
              </el-icon>
            </div>
            <div class="tab-text">
              <span class="tab-title">预览</span>
              <span class="tab-desc">预览效果</span>
            </div>
            <div class="tab-status" v-if="videoCompleted">
              <el-icon v-if="animationResult.status === 'completed'" class="status-icon completed">
                <Check />
              </el-icon>
            </div>
          </div>
        </template>
        <div class="result-content">
          <div v-if="!storyboards" class="empty-state final-empty-state">
            <el-icon class="empty-icon"><Star /></el-icon>
            <div class="empty-text">暂无预览</div>
            <div class="empty-desc">请先完成前面的创作步骤</div>
          </div>
          <VideoPreview v-else :creative-text="creativeText || '幕夜与景莲子'" :video-progress="videoProgress"
            :volume-level="volumeLevel" @update:video-progress="$emit('update:videoProgress', $event)"
            @update:volume-level="$emit('update:volumeLevel', $event)" @generate-video="showGenerateDialog"
            @generate-share="$emit('generation-records')"
            :storyboards="storyboards"
            :show-generate-share="true" 
            :show-generate-video="true" 
            :aspect-ratio="props.outputRatio" />
        </div>
      </el-tab-pane> -->

      <!-- 新增生成记录标签页 -->
      <!-- <el-tab-pane name="records"> -->
        <!-- <template #label>
          <div class="tab-label">
            <div class="tab-icon">
              <el-icon>
                <Collection />
              </el-icon>
            </div>
            <div class="tab-text">
              <span class="tab-title">作品</span>
              <span class="tab-desc">有声故事</span>
            </div>
          </div>
        </template> -->
        <!-- <RecordComponent :conversation-id="conversationId" :active-tab="activeTab"
          @update-records-count="handleRecordsCountUpdate" /> -->
      <!-- </el-tab-pane> -->
    </el-tabs>

    <!-- 生成视频弹窗 -->
    <el-dialog v-model="generateDialogVisible" title="生成视频" width="400px" :close-on-click-modal="false">
      <div class="generate-dialog-content">
        <div class="cost-info">
          <el-icon>
            <Coin />
          </el-icon>
          <span class="cost-amount">50</span>
          <span class="cost-label">金币</span>
        </div>
        <div class="time-info">
          <el-icon>
            <Timer />
          </el-icon>
          <span class="time-amount">约 5-10 分钟</span>
        </div>
        <div class="balance-info">
          当前余额：<span class="balance-amount">100</span> 金币
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleGenerateVideo">确认生成</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ref, defineProps, defineEmits, toRefs, computed, watch, onMounted } from 'vue'
import { Check, Loading, VideoCamera, Edit, Delete, User, Picture, Star, Connection, Document, Timer, Coin, Download, Share, ArrowDown, SetUp, Collection, Pointer } from '@element-plus/icons-vue'
import StoryGeneration from './StoryGeneration.vue'
import CharacterGeneration from './CharacterGeneration.vue'
import ShotGeneration from './ShotGeneration.vue'
import StoryDesign from './StoryDesign.vue'
import RecordComponent from './RecordComponent.vue'

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  },
  scriptResult: {
    type: Object,
    default: null
  },
  voiceResult: {
    type: Object,
    default: null
  },
  editResult: {
    type: Object,
    default: null
  },
  animationResult: {
    type: Object,
    default: null
  },
  videoCompleted: {
    type: Boolean,
    default: false
  },
  creativeText: {
    type: String,
    default: ''
  },
  styleName: {
    type: String,
    default: ''
  },
  videoProgress: {
    type: Number,
    default: 0
  },
  volumeLevel: {
    type: Number,
    default: 80
  },
  showShareOptions: {
    type: Boolean,
    default: false
  },
  shotStory: { // 生成的故事
    type: Object,
    default: null
  },
  shotScenes: { // 生成的场景
    type: Object,
    default: null
  },
  shotRoles: { // 生成的角色
    type: Object,
    default: null
  },
  shotShots: { // 生成的分镜
    type: Object,
    default: null
  },
  storyboards: { // 生成的分镜预览
    type: Object,
    default: null
  },
  designResult: {
    type: Object,
    default: null
  },
  storyDesign: {
    type: Object,
    default: null
  },
  conversationId: {
    type: String,
    default: ''
  },
  chatPanelVisible: {
    type: Boolean,
    default: true
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  },
  sessionResult: {
    type: Object,
    default: null
  },
})




const emit = defineEmits([
  'update:activeTab',
  'update:videoProgress',
  'update:volumeLevel',
  'toggle-share-options',
  'download-video',
  'share-to-media',
  'copy-share-link',
  'fetch-ai-conversations-content-list',
  'generation-records',
  'update:storyDesign',
  'update:shotStory',
  'update:shotShots',
  'update-records-count',
  'update-shot-shots-count',
  'refresh-voices',
  'update:shotRoles',
  'refresh-project-data'
])


// 创作步骤
const creationSteps = computed(() => [
  {
    label: '故事设计',
    completed: props.designResult && props.designResult.status === 'completed',
    current: props.activeTab === 'design' && (!props.designResult || props.designResult.status !== 'completed')
  },
  {
    label: '故事生成',
    completed: props.scriptResult && props.scriptResult.status === 'completed',
    current: props.activeTab === 'script' && (!props.scriptResult || props.scriptResult.status !== 'completed')
  },
  {
    label: '场景生成',
    completed: props.voiceResult && props.voiceResult.status === 'completed',
    current: props.activeTab === 'voice' && (!props.voiceResult || props.voiceResult.status !== 'completed')
  },
  {
    label: '角色生成',
    completed: props.editResult && props.editResult.status === 'completed',
    current: props.activeTab === 'edit' && (!props.editResult || props.editResult.status !== 'completed')
  },
  {
    label: '分镜生成',
    completed: props.animationResult && props.animationResult.status === 'completed',
    current: props.activeTab === 'animation' && (!props.animationResult || props.animationResult.status !== 'completed')
  },
  {
    label: '分镜预览',
    completed: props.videoCompleted,
    current: props.activeTab === 'final' && !props.videoCompleted
  }
])

// 计算总体创作进度百分比
const creationProgressPercentage = computed(() => {
  let completedSteps = 0
  let totalSteps = creationSteps.value.length

  creationSteps.value.forEach(step => {
    if (step.completed) completedSteps++
  })

  return Math.round((completedSteps / totalSteps) * 100)
})


const generateDialogVisible = ref(false)

const router = useRouter()

const showGenerateDialog = () => {
  generateDialogVisible.value = true
}

const handleGenerateVideo = () => {
  generateDialogVisible.value = false
  ElMessage.success('视频生成任务已开始，请耐心等待...')
  // 触发父组件的生成视频事件
  emit('generate-video')
}

// 处理记录数量更新
const handleRecordsCountUpdate = (count) => {
  emit('update-records-count', count)
}

const shotsCount = ref(0)
// 处理分镜章节数量更新
const handleShotShotsCountUpdate = (count) => {
  shotsCount.value = count
  emit('update-shot-shots-count', count)
}

// 监视activeTab的变化，当切换到final标签页时自动进入全屏模式
let isFirstLoad = true
watch(() => props.activeTab, (newValue) => {
  console.log('CreationProcess中activeTab变化为:', newValue)

  const executeTabActions = () => {
    if (newValue === 'design') {
      emit('fetch-ai-conversations-content-list', { contentType: 10 })
    } else if (newValue === 'script') {
      emit('fetch-ai-conversations-content-list', { contentType: 1 })
    } else if (newValue === 'voice') {
      emit('fetch-ai-conversations-content-list', { contentType: 2 })
    } else if (newValue === 'edit') {
      emit('fetch-ai-conversations-content-list', { contentType: 3 })
    } else if (newValue === 'animation') {
      // emit('fetch-ai-conversations-content-list', { contentType: 4 })
    } else if (newValue === 'final') {
      emit('fetch-ai-conversations-content-list', { contentType: 5 })
      // 自动全屏
      // const storyPlayerElement = document.querySelector('.story-player')
      // if (storyPlayerElement && !document.fullscreenElement) {
      //   try {
      //     storyPlayerElement.requestFullscreen().catch(err => {
      //       console.error('自动全屏失败:', err)
      //     })
      //   } catch (error) {
      //     console.error('自动全屏出错:', error)
      //   }
      // }
    }
  }

  // 只有第一次加载时延迟执行，避免首次加载时conversationId为null
  if (isFirstLoad) {
    setTimeout(() => {
      executeTabActions()
      isFirstLoad = false
    }, 300)
  } else {
    executeTabActions()
  }
}, { immediate: true })

// 监听关键props的变化，确保组件正确响应数据更新
watch(() => props.shotRoles, (newVal, oldVal) => {
  console.log('CreationProcess - shotRoles变化:',
    '新角色数量:', newVal?.characters?.length,
    '旧角色数量:', oldVal?.characters?.length)
}, { deep: true })

watch(() => props.shotScenes, (newVal, oldVal) => {
  console.log('CreationProcess - shotScenes变化:',
    '新场景数量:', newVal?.scenes?.length,
    '旧场景数量:', oldVal?.scenes?.length)
}, { deep: true })

watch(() => props.shotShots, (newVal, oldVal) => {
  console.log('CreationProcess - shotShots变化:',
    '新分镜组数量:', newVal?.shotGroups?.length,
    '旧分镜组数量:', oldVal?.shotGroups?.length)
}, { deep: true })

watch(() => props.storyDesign, (newVal) => {
  console.log('CreationProcess - storyDesign变化:', newVal ? '有数据' : '无数据')
}, { deep: true })

watch(() => props.shotStory, (newVal) => {
  console.log('CreationProcess - shotStory变化:',
    '章节数量:', newVal?.chapters?.length)
}, { deep: true })

const handleStoryDesignUpdate = (updatedStoryDesign) => {
  emit('update:storyDesign', updatedStoryDesign)
}

const handleShotStoryUpdate = (updatedShotStory) => {
  emit('update:shotStory', updatedShotStory)
}

const handleShotShotsUpdate = (updatedShotShots) => {
  emit('update:shotShots', updatedShotShots)
}

const handleShotRolesUpdate = (updatedShotRoles) => {
  emit('update:shotRoles', updatedShotRoles)
}

// 图片比例状态
const imageRatio = ref('9:16')

// 处理图片比例更新
const handleUpdateRatio = (newRatio) => {
  console.log('CreationProcess - 接收到图片比例更新:', newRatio)
  imageRatio.value = newRatio
}

// 监听chatPanelVisible的变化
watch(() => props.chatPanelVisible, (newVal) => {
  console.log('CreationProcess - 聊天面板可见状态变化:', newVal ? '可见' : '隐藏')
}, { immediate: true })



</script>

<style scoped>
.creation-process {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  /* 确保作为绝对定位的参考点 */
  /* background-color: #10b981; */
}

/* 根据聊天面板状态控制tabs header的显示/隐藏 */
.creation-process.chat-visible :deep(.creation-tabs > .el-tabs__header) {
  display: none;
}

/* @media (max-width: 768px) {
  .creation-process.chat-visible :deep(.creation-tabs > .el-tabs__header) {
    display: flex;
  }

  .creation-process.chat-visible .tab-icon {
    display: none;
  }
} */

:deep(.el-tabs__nav-wrap) {
  padding: 0 34px;
  /* 为左右按钮留出空间 */
  overflow: hidden;
  position: relative;
}

:deep(.el-tabs__nav-scroll) {
  overflow: hidden;
  position: relative;
}

/* 增大前后导航按钮样式 */
:deep(.el-tabs__nav-prev),
:deep(.el-tabs__nav-next) {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 18px;
  color: #6366f1;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
  margin: auto 8px;
  transition: all 0.3s;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-tabs__nav-prev) {
  left: 0;
}

:deep(.el-tabs__nav-next) {
  right: 0;
}

:deep(.el-tabs__nav-prev:hover),
:deep(.el-tabs__nav-next:hover) {
  color: white;
  background-color: #6366f1;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
  transform: translateY(-50%) scale(1.1);
}

:deep(.el-tabs__nav-prev i),
:deep(.el-tabs__nav-next i) {
  font-weight: bold;
  font-size: 16px;
}

/* 添加点击效果 */
:deep(.el-tabs__nav-prev:active),
:deep(.el-tabs__nav-next:active) {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 1px 3px rgba(99, 102, 241, 0.2);
}

/* 当没有更多标签可滚动时的样式 */
:deep(.el-tabs__nav-prev.is-disabled),
:deep(.el-tabs__nav-next.is-disabled) {
  color: #c0c4cc;
  /* background-color: rgba(240, 240, 240, 0.8); */
  box-shadow: none;
  cursor: not-allowed;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
  /* background-color: #000; */
  background-color: transparent;
  position: relative;
  transition: all 0.3s ease;
}

/* 聊天面板隐藏时，内容区域需要调整以填充头部区域留下的空间 */
.creation-process.chat-visible :deep(.el-tabs__content) {
  height: 100%;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow-y: auto;
  padding: 0px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

/* 为最后一个tab-pane（分镜预览）特殊处理 */
:deep(.el-tabs__content .el-tab-pane[name="final"]) {
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 调整视频预览样式 */
.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
  height: 100%;
}

/* 确保VideoPreview组件能够填充整个可用空间 */
.result-content :deep(.story-player) {
  flex: 1;
  margin: 0;
  border-radius: 0;
  height: 100%;
  width: 100%;
  /* border-radius: 12px; */
  /* margin-bottom: 12px; */
}

/* 滚动条样式优化 */
:deep(.el-tab-pane)::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

:deep(.el-tab-pane)::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: rgba(99, 102, 241, 0.2);
  transition: all 0.3s;
}

:deep(.el-tab-pane)::-webkit-scrollbar-thumb:hover {
  background-color: rgba(99, 102, 241, 0.4);
}

:deep(.el-tab-pane)::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

/* 标签页样式优化 */
.creation-tabs {
  --tab-height: 72px;
}

:deep(.el-tabs__nav) {
  height: var(--tab-height);
}

:deep(.el-tabs__item) {
  height: var(--tab-height) !important;
  transition: all 0.3s ease;
  /* padding: 0 14px !important; */
}

/* 为标签添加底部指示器 */
:deep(.el-tabs__active-bar) {
  height: 2px;
  background-color: #6366f1;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 13px;
  height: 100%;
  padding: 12px 0;
}

.tab-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
  font-size: 20px;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item.is-active) .tab-icon {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  transform: scale(1.05);
}

.tab-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: left;
}

.tab-title {
  font-size: 16px;
  font-weight: 500;
  color: #3d3d3d;
  line-height: 1.2;
}

.dark .tab-title {
  color: #e7f3ff;
}

.tab-desc {
  font-size: 12px;
  color: #64748b;
  line-height: 1.2;
}

:deep(.el-tabs__item.is-active) .tab-title {
  color: #6366f1;
  font-weight: 600;
}

:deep(.el-tabs__item.is-active) .tab-desc {
  color: #818cf8;
}

.tab-status {
  margin-left: auto;
}

.status-icon {
  font-size: 18px;
  line-height: 1;
}

.status-icon.completed {
  color: #10b981;
}

.status-icon.in-progress {
  color: #f59e0b;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .creation-tabs {
    --tab-height: 60px;
  }

  :deep(.el-tabs__item) {
    padding: 0 8px !important;
  }

  .tab-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
    display: none;
  }

  .tab-title {
    font-size: 14px;
  }

  .tab-desc {
    font-size: 11px;
  }

  :deep(.el-tabs__content) {
    padding: 12px;
  }

  :deep(.el-tab-pane) {
    padding: 12px;
  }

  :deep(.el-tabs__content .el-tab-pane[name="final"]) {
    padding: 0;
  }

  .tab-label {
    gap: 8px;
  }

  /* 移动设备上的导航按钮样式 */
  :deep(.el-tabs__nav-prev),
  :deep(.el-tabs__nav-next) {
    width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 16px;
    margin: auto 4px;
  }

  :deep(.el-tabs__nav-prev i),
  :deep(.el-tabs__nav-next i) {
    font-size: 14px;
  }

  /* 调整滚动条在移动设备上的样式 */
  :deep(.el-tab-pane)::-webkit-scrollbar {
    width: 4px;
  }
}

@media (max-width: 480px) {
  .tab-desc {
    display: none;
  }

  .tab-icon {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  :deep(.el-tabs__item) {
    padding: 0 6px !important;
  }

  :deep(.el-tabs__header) {
    padding: 0;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 5px;
  }

  .tab-label {
    gap: 6px;
  }

  /* 小屏幕上的导航按钮样式 */
  :deep(.el-tabs__nav-prev),
  :deep(.el-tabs__nav-next) {
    width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    margin: auto 2px;
    box-shadow: 0 1px 3px rgba(99, 102, 241, 0.15);
  }

  :deep(.el-tabs__nav-prev i),
  :deep(.el-tabs__nav-next i) {
    font-size: 12px;
  }
}

.thought-content {
  padding: 10px;
  color: #606266;
}

/* 生成视频弹窗样式 */
.generate-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 20px 0;
}

/* 确保对话框显示在最上层 */
/* :deep(.el-dialog) {
  z-index: 3000 !important;
}

:deep(.el-overlay) {
  z-index: 2999 !important;
} */

:deep(.ax-thought-chain) {
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 16px;
  margin-top: 10px;
}

:deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #4d72f6 0, #4d72f6 100%,
      transparent 0, transparent);
}

:deep(.el-tabs__nav-wrap::after) {
  position: static !important;
}

/* 确保所有tab内容都能正确滚动 */
:deep(.story-scenes),
:deep(.scene-designs),
:deep(.role-design-section),
:deep(.shot-designs) {
  padding-bottom: 40px;
}

/* 特别处理移动设备上的样式 */
@media (max-width: 768px) {
  :deep(.el-tab-pane) {
    padding: 12px;
  }

  :deep(.el-tabs__content .el-tab-pane[name="final"]) {
    padding: 0;
  }
}

.video-toolbar {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 添加空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 120px);
  padding: 20px;
  text-align: center;
  /* background-color: rgba(250, 250, 250, 0.7); */
  border-radius: 12px;
}

/* body.dark .empty-state {
  background-color: rgba(30, 30, 30, 0.5);
} */

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

body.dark .empty-icon {
  color: #606266;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

body.dark .empty-text {
  color: #e0e0e0;
}

.empty-desc {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-desc {
  color: #a0a0a0;
}

/* 添加轻微动画 */
.empty-icon {
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 分镜预览空状态特殊样式 */
.final-empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  border-radius: 0;
  /* z-index: 10; */
  /* background-color: rgba(255, 255, 255, 0.9); */
  /* backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px); */
}

.final-empty-state .empty-icon {
  font-size: 64px;
  color: #6365f1ca;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: floating 3s ease-in-out infinite;
}

.final-empty-state .empty-text {
  font-size: 22px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 10px;
}

.final-empty-state .empty-desc {
  font-size: 16px;
  color: #6b7280;
  max-width: 400px;
  line-height: 1.5;
}

/* body.dark .final-empty-state {
  background-color: rgba(20, 20, 20, 0.8);
} */

body.dark .final-empty-state .empty-text {
  color: #e5e7eb;
}

body.dark .final-empty-state .empty-desc {
  color: #9ca3af;
}

@keyframes floating {

  0%,
  100% {
    transform: translateY(0);
    scale: 1;
  }

  50% {
    transform: translateY(-15px);
    scale: 1.05;
  }
}

/* 聊天面板相关样式 */
.creation-process.chat-visible :deep(.el-tabs__content) {
  height: 100%;
}
</style>